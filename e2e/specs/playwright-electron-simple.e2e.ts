import { _electron as electron } from 'playwright';
import type { ElectronApplication, Page } from 'playwright';
import { expect, test, beforeAll, afterAll } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Simple Playwright Electron Test
 *
 * This test verifies that our unpacked Obsidian approach works
 * and demonstrates the basic Playwright Electron functionality.
 */

const appPath = path.resolve('./.obsidian-unpacked/main.js');
const vaultPath = path.resolve('./tests/vault/Test');
const userDataDir = path.resolve('./e2e/test_obsidian_data');

describe("Playwright Electron - Simple Test", () => {
  let electronApp: ElectronApplication;
  let page: Page;

  beforeAll(async () => {
    // Check if Obsidian is unpacked
    if (!fs.existsSync(appPath)) {
      throw new Error(
        `Unpacked Obsidian not found at ${appPath}. ` +
        'Please run: npm run setup:obsidian-playwright'
      );
    }

    console.log("🚀 Launching unpacked Obsidian with <PERSON><PERSON>...");

    // Launch unpacked Obsidian using Playwright Electron
    electronApp = await electron.launch({
      args: [
        appPath,
        '--user-data-dir=' + userDataDir,
        'open',
        `obsidian://open?path=${encodeURIComponent(vaultPath)}`,
      ],
      timeout: 30000,
    });

    console.log("✅ Obsidian launched successfully");

    // Get the first window
    page = await electronApp.firstWindow();
    console.log("📱 Got main window, title:", await page.title());

    // Wait for Obsidian to be fully loaded
    await page.waitForFunction(() => {
      return typeof (window as any).app !== 'undefined' &&
             (window as any).app.workspace !== undefined;
    }, { timeout: 15000 });

    console.log("✅ Obsidian app object is ready");
  });

  afterAll(async () => {
    if (electronApp) {
      console.log("🔄 Closing Electron app...");
      await electronApp.close();
      console.log("✅ Electron app closed");
    }
  });

  test("should successfully launch Obsidian and access basic functionality", async () => {
    // Verify we can access the Obsidian app object
    const appInfo = await page.evaluate(() => {
      const app = (window as any).app;
      return {
        hasApp: typeof app !== 'undefined',
        hasWorkspace: typeof app?.workspace !== 'undefined',
        hasVault: typeof app?.vault !== 'undefined',
        hasCommands: typeof app?.commands !== 'undefined',
        vaultName: app?.vault?.getName?.(),
        commandCount: Object.keys(app?.commands?.commands || {}).length,
      };
    });

    console.log("📊 App info:", appInfo);

    expect(appInfo.hasApp).toBe(true);
    expect(appInfo.hasWorkspace).toBe(true);
    expect(appInfo.hasVault).toBe(true);
    expect(appInfo.hasCommands).toBe(true);
    expect(appInfo.vaultName).toBe('Test');
    expect(appInfo.commandCount).toBeGreaterThan(0);

    console.log("✅ Basic Obsidian functionality verified");
  });

  test("should be able to interact with the UI", async () => {
    // Try to open the command palette
    await page.keyboard.press('Meta+P'); // Cmd+P on Mac

    // Wait for command palette to appear
    await page.waitForSelector('.prompt-input', { timeout: 5000 });

    console.log("✅ Command palette opened successfully");

    // Type a command
    await page.fill('.prompt-input', 'help');

    // Wait for suggestions to appear
    await page.waitForSelector('.suggestion-item', { timeout: 2000 });

    const suggestionCount = await page.locator('.suggestion-item').count();
    console.log(`📋 Found ${suggestionCount} command suggestions`);

    expect(suggestionCount).toBeGreaterThan(0);

    // Close the command palette
    await page.keyboard.press('Escape');

    console.log("✅ UI interaction test completed");
  });

  test("should demonstrate HAR recording capability", async () => {
    // This test doesn't make network requests, but demonstrates that
    // HAR recording would work if we enabled it in the launch options

    const hasNetworkCapability = await page.evaluate(() => {
      return typeof fetch !== 'undefined' && typeof XMLHttpRequest !== 'undefined';
    });

    expect(hasNetworkCapability).toBe(true);

    console.log("✅ Network capabilities available for HAR recording");
    console.log("💡 To enable HAR recording, add recordHar option to electron.launch()");
  });
});
